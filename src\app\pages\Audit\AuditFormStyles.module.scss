// Modern Audit Forms Design System
// Enhanced UI/UX with attractive colors and modern styling

// Color Palette
$good-practices-primary: #10b981;
$good-practices-secondary: #059669;
$good-practices-light: #d1fae5;
$good-practices-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);

$improvement-primary: #f59e0b;
$improvement-secondary: #d97706;
$improvement-light: #fef3c7;
$improvement-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);

$nonconformance-primary: #ef4444;
$nonconformance-secondary: #dc2626;
$nonconformance-light: #fee2e2;
$nonconformance-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

// Neutral Colors
$white: #ffffff;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Typography
$font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-base: 1rem;
$font-size-lg: 1.125rem;
$font-size-xl: 1.25rem;
$font-size-2xl: 1.5rem;
$font-size-3xl: 1.875rem;

// Spacing
$spacing-1: 0.25rem;
$spacing-2: 0.5rem;
$spacing-3: 0.75rem;
$spacing-4: 1rem;
$spacing-5: 1.25rem;
$spacing-6: 1.5rem;
$spacing-8: 2rem;
$spacing-10: 2.5rem;
$spacing-12: 3rem;

// Border Radius
$radius-sm: 0.375rem;
$radius-md: 0.5rem;
$radius-lg: 0.75rem;
$radius-xl: 1rem;
$radius-2xl: 1.5rem;

// Shadows
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// Base Container
.auditFormContainer {
  max-width: 900px;
  margin: 0 auto;
  padding: $spacing-6;
  font-family: $font-family;
  background: $gray-50;
  min-height: 100vh;
}

// Header Styles
.formHeader {
  text-align: center;
  margin-bottom: $spacing-8;
  padding: $spacing-8 $spacing-6;
  background: $white;
  border-radius: $radius-2xl;
  box-shadow: $shadow-lg;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--header-gradient);
  }
}

.formTitle {
  font-size: $font-size-3xl;
  font-weight: 700;
  margin-bottom: $spacing-3;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-3;
  
  i {
    font-size: $font-size-2xl;
    padding: $spacing-3;
    border-radius: 50%;
    background: var(--light-color);
    color: var(--primary-color);
  }
}

.formSubtitle {
  font-size: $font-size-lg;
  color: $gray-600;
  font-weight: 400;
  line-height: 1.6;
}

// Card Styles
.formCard {
  background: $white;
  border-radius: $radius-xl;
  box-shadow: $shadow-md;
  margin-bottom: $spacing-6;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid $gray-200;
  
  &:hover {
    box-shadow: $shadow-xl;
    transform: translateY(-2px);
  }
}

.cardHeader {
  background: linear-gradient(135deg, $gray-50 0%, $white 100%);
  padding: $spacing-5 $spacing-6;
  border-bottom: 1px solid $gray-200;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--header-gradient);
  }
}

.cardTitle {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $gray-800;
  margin: 0;
  display: flex;
  align-items: center;
  gap: $spacing-2;
  
  i {
    color: var(--primary-color);
    font-size: $font-size-xl;
  }
}

.cardBody {
  padding: $spacing-6;
}

// Form Elements
.formGroup {
  margin-bottom: $spacing-6;
}

.formLabel {
  display: block;
  font-size: $font-size-sm;
  font-weight: 600;
  color: $gray-700;
  margin-bottom: $spacing-2;
  
  .required {
    color: var(--primary-color);
    margin-left: $spacing-1;
  }
}

.formControl, .formSelect {
  width: 100%;
  padding: $spacing-4 $spacing-4;
  font-size: $font-size-base;
  border: 2px solid $gray-300;
  border-radius: $radius-lg;
  background: $white;
  transition: all 0.2s ease-in-out;
  font-family: $font-family;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--focus-ring);
  }
  
  &::placeholder {
    color: $gray-400;
  }
}

.formControlLg {
  padding: $spacing-5 $spacing-5;
  font-size: $font-size-lg;
}

.textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
}

// Upload Zone
.uploadZone {
  border: 2px dashed $gray-300;
  border-radius: $radius-lg;
  padding: $spacing-8;
  text-align: center;
  transition: all 0.3s ease;
  background: $gray-50;
  
  &:hover {
    border-color: var(--primary-color);
    background: var(--light-color);
  }
  
  &.dragActive {
    border-color: var(--primary-color);
    background: var(--light-color);
    transform: scale(1.02);
  }
}

// Processing Indicator
.processingIndicator {
  background: var(--light-color);
  border: 1px solid var(--primary-color);
  border-radius: $radius-lg;
  padding: $spacing-4 $spacing-5;
  margin-bottom: $spacing-4;
  color: var(--primary-color);
  font-weight: 500;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-2;
  
  .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--primary-color);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Theme Variants
.goodPractices {
  --primary-color: #{$good-practices-primary};
  --secondary-color: #{$good-practices-secondary};
  --light-color: #{$good-practices-light};
  --header-gradient: #{$good-practices-gradient};
  --focus-ring: #{rgba($good-practices-primary, 0.2)};
}

.improvement {
  --primary-color: #{$improvement-primary};
  --secondary-color: #{$improvement-secondary};
  --light-color: #{$improvement-light};
  --header-gradient: #{$improvement-gradient};
  --focus-ring: #{rgba($improvement-primary, 0.2)};
}

.nonConformance {
  --primary-color: #{$nonconformance-primary};
  --secondary-color: #{$nonconformance-secondary};
  --light-color: #{$nonconformance-light};
  --header-gradient: #{$nonconformance-gradient};
  --focus-ring: #{rgba($nonconformance-primary, 0.2)};
}

// Button Styles
.btnPrimary {
  background: var(--header-gradient);
  border: none;
  color: $white;
  padding: $spacing-3 $spacing-6;
  border-radius: $radius-lg;
  font-weight: 600;
  font-size: $font-size-base;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: $spacing-2;
  box-shadow: $shadow-md;

  &:hover {
    transform: translateY(-1px);
    box-shadow: $shadow-lg;
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

.btnSecondary {
  background: $white;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  padding: $spacing-3 $spacing-6;
  border-radius: $radius-lg;
  font-weight: 600;
  font-size: $font-size-base;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: $spacing-2;

  &:hover {
    background: var(--primary-color);
    color: $white;
    transform: translateY(-1px);
  }
}

// Progress Indicator
.progressBar {
  width: 100%;
  height: 6px;
  background: $gray-200;
  border-radius: $radius-sm;
  overflow: hidden;
  margin-bottom: $spacing-6;

  .progressFill {
    height: 100%;
    background: var(--header-gradient);
    transition: width 0.3s ease;
    border-radius: $radius-sm;
  }
}

// Status Badges
.statusBadge {
  display: inline-flex;
  align-items: center;
  gap: $spacing-1;
  padding: $spacing-1 $spacing-3;
  border-radius: $radius-md;
  font-size: $font-size-xs;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;

  &.success {
    background: $good-practices-light;
    color: $good-practices-secondary;
  }

  &.warning {
    background: $improvement-light;
    color: $improvement-secondary;
  }

  &.danger {
    background: $nonconformance-light;
    color: $nonconformance-secondary;
  }
}

// Form Text and Help
.formText {
  font-size: $font-size-sm;
  color: $gray-500;
  margin-top: $spacing-2;
  line-height: 1.5;

  &.error {
    color: $nonconformance-primary;
  }

  &.success {
    color: $good-practices-primary;
  }
}

// Animation Classes
.fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.slideUp {
  animation: slideUp 0.5s ease-out;
}

.scaleIn {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Form Divider
.formDivider {
  margin: $spacing-8 0;
  position: relative;
  text-align: center;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: $gray-300;
  }

  .dividerTitle {
    background: $gray-50;
    padding: 0 $spacing-4;
    color: $gray-500;
    font-size: $font-size-sm;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

// Validation Message
.validationMessage {
  display: flex;
  align-items: center;
  gap: $spacing-2;
  padding: $spacing-3 $spacing-4;
  border-radius: $radius-md;
  font-size: $font-size-sm;
  font-weight: 500;
  margin-top: $spacing-2;

  &.error {
    background: $nonconformance-light;
    color: $nonconformance-secondary;
    border: 1px solid rgba($nonconformance-primary, 0.3);
  }

  &.success {
    background: $good-practices-light;
    color: $good-practices-secondary;
    border: 1px solid rgba($good-practices-primary, 0.3);
  }

  &.warning {
    background: $improvement-light;
    color: $improvement-secondary;
    border: 1px solid rgba($improvement-primary, 0.3);
  }

  i {
    font-size: $font-size-base;
  }
}

// Loading Spinner
.loadingSpinner {
  display: flex;
  align-items: center;
  justify-content: center;

  &.small .spinner {
    width: 12px;
    height: 12px;
    border-width: 1px;
  }

  &.medium .spinner {
    width: 20px;
    height: 20px;
    border-width: 2px;
  }

  &.large .spinner {
    width: 32px;
    height: 32px;
    border-width: 3px;
  }
}

// Enhanced Select with Icons
.formSelect {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 40px;
  appearance: none;
}

// File Upload Enhancements
.uploadZone {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 48px;
    height: 48px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0.1;
    transition: all 0.3s ease;
  }

  &:hover::before,
  &.dragActive::before {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

.uploadIcon {
  font-size: $font-size-3xl;
  color: var(--primary-color);
  margin-bottom: $spacing-3;
}

.uploadText {
  color: $gray-600;
  font-size: $font-size-base;
  font-weight: 500;
  margin-bottom: $spacing-2;
}

.uploadSubtext {
  color: $gray-400;
  font-size: $font-size-sm;
}

// Responsive Design
@media (max-width: 768px) {
  .auditFormContainer {
    padding: $spacing-4;
  }

  .formHeader {
    padding: $spacing-6 $spacing-4;
  }

  .formTitle {
    font-size: $font-size-2xl;
    flex-direction: column;
    gap: $spacing-2;
  }

  .cardBody {
    padding: $spacing-4;
  }

  .btnPrimary, .btnSecondary {
    width: 100%;
    justify-content: center;
  }
}
