import React from 'react';
import styles from './AuditFormStyles.module.scss';

// Form Header Component
export const FormHeader = ({ title, subtitle, icon, theme = 'goodPractices' }) => (
  <div className={`${styles.formHeader} ${styles.fadeIn}`}>
    <h2 className={styles.formTitle}>
      <i className={icon}></i>
      {title}
    </h2>
    <p className={styles.formSubtitle}>
      {subtitle}
    </p>
  </div>
);

// Form Card Component
export const FormCard = ({ title, icon, children, className = '' }) => (
  <div className={`${styles.formCard} ${styles.slideUp} ${className}`}>
    <div className={styles.cardHeader}>
      <h5 className={styles.cardTitle}>
        <i className={icon}></i>
        {title}
      </h5>
    </div>
    <div className={styles.cardBody}>
      {children}
    </div>
  </div>
);

// Form Group Component
export const FormGroup = ({ label, required = false, children, helpText, error }) => (
  <div className={styles.formGroup}>
    <label className={styles.formLabel}>
      {label}
      {required && <span className={styles.required}>*</span>}
    </label>
    {children}
    {helpText && (
      <div className={`${styles.formText} ${error ? styles.error : ''}`}>
        {helpText}
      </div>
    )}
  </div>
);

// Enhanced Input Component
export const FormInput = ({ 
  type = 'text', 
  placeholder, 
  value, 
  onChange, 
  name, 
  id, 
  required = false,
  large = false,
  className = '',
  ...props 
}) => (
  <input
    type={type}
    className={`${styles.formControl} ${large ? styles.formControlLg : ''} ${className}`}
    placeholder={placeholder}
    value={value}
    onChange={onChange}
    name={name}
    id={id}
    required={required}
    {...props}
  />
);

// Enhanced Select Component
export const FormSelect = ({ 
  value, 
  onChange, 
  name, 
  id, 
  required = false,
  large = false,
  children,
  className = '',
  ...props 
}) => (
  <select
    className={`${styles.formSelect} ${large ? styles.formControlLg : ''} ${className}`}
    value={value}
    onChange={onChange}
    name={name}
    id={id}
    required={required}
    {...props}
  >
    {children}
  </select>
);

// Enhanced Textarea Component
export const FormTextarea = ({ 
  placeholder, 
  value, 
  onChange, 
  name, 
  id, 
  required = false,
  rows = 4,
  large = false,
  className = '',
  ...props 
}) => (
  <textarea
    className={`${styles.formControl} ${styles.textarea} ${large ? styles.formControlLg : ''} ${className}`}
    placeholder={placeholder}
    value={value}
    onChange={onChange}
    name={name}
    id={id}
    required={required}
    rows={rows}
    {...props}
  />
);

// Processing Indicator Component
export const ProcessingIndicator = ({ message = 'Processing files...' }) => (
  <div className={styles.processingIndicator}>
    <div className={styles.spinner}></div>
    {message}
  </div>
);

// Status Badge Component
export const StatusBadge = ({ type = 'success', children }) => (
  <span className={`${styles.statusBadge} ${styles[type]}`}>
    {children}
  </span>
);

// Progress Bar Component
export const ProgressBar = ({ progress = 0 }) => (
  <div className={styles.progressBar}>
    <div 
      className={styles.progressFill} 
      style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
    ></div>
  </div>
);

// Enhanced Button Component
export const FormButton = ({ 
  type = 'button',
  variant = 'primary',
  children,
  onClick,
  disabled = false,
  icon,
  className = '',
  ...props 
}) => (
  <button
    type={type}
    className={`${variant === 'primary' ? styles.btnPrimary : styles.btnSecondary} ${className}`}
    onClick={onClick}
    disabled={disabled}
    {...props}
  >
    {icon && <i className={icon}></i>}
    {children}
  </button>
);

// Upload Zone Component
export const UploadZone = ({ 
  onDrop, 
  isDragActive = false, 
  children, 
  className = '' 
}) => (
  <div 
    className={`${styles.uploadZone} ${isDragActive ? styles.dragActive : ''} ${className}`}
    onDrop={onDrop}
  >
    {children}
  </div>
);

// Form Container Component
export const FormContainer = ({ theme = 'goodPractices', children, className = '' }) => (
  <div className={`${styles.auditFormContainer} ${styles[theme]} ${className}`}>
    {children}
  </div>
);

// Category Option Component with Icons
export const CategoryOption = ({ value, icon, label, description }) => (
  <option value={value} data-icon={icon}>
    {icon} {label}
  </option>
);

// Enhanced File Upload Info Component
export const FileUploadInfo = ({ acceptedTypes, maxSize }) => (
  <div className={styles.formText}>
    <small>
      <strong>Accepted formats:</strong> {acceptedTypes.join(', ')} | 
      <strong> Max size:</strong> {maxSize}MB per file
    </small>
  </div>
);

// Form Section Divider
export const FormDivider = ({ title }) => (
  <div className={styles.formDivider}>
    {title && <span className={styles.dividerTitle}>{title}</span>}
  </div>
);

// Validation Message Component
export const ValidationMessage = ({ type = 'error', message, show = false }) => {
  if (!show || !message) return null;
  
  return (
    <div className={`${styles.validationMessage} ${styles[type]} ${styles.fadeIn}`}>
      <i className={type === 'error' ? 'mdi mdi-alert-circle' : 'mdi mdi-check-circle'}></i>
      {message}
    </div>
  );
};

// Loading Spinner Component
export const LoadingSpinner = ({ size = 'medium', color = 'primary' }) => (
  <div className={`${styles.loadingSpinner} ${styles[size]} ${styles[color]}`}>
    <div className={styles.spinner}></div>
  </div>
);

export default {
  FormHeader,
  FormCard,
  FormGroup,
  FormInput,
  FormSelect,
  FormTextarea,
  ProcessingIndicator,
  StatusBadge,
  ProgressBar,
  FormButton,
  UploadZone,
  FormContainer,
  CategoryOption,
  FileUploadInfo,
  FormDivider,
  ValidationMessage,
  LoadingSpinner
};
