import React, { useState, useEffect } from "react";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../../services/API";
import { AUDIT_FINDINGS_ASSIGN_ACTION_URL, AUDIT_GMS3_URL, AUDIT_WITH_ID_URL, GET_USERS_BY_ROLE, INSPECTION_ACTION_PLAN_REVIEWER, STATIC_URL, AUDIT_GMS3_WITH_ID_URL, AUDIT_GMS2_WITH_ID_URL } from "../../constants";
import { processUploadedFiles, getAcceptedFileTypes } from "./utils/imageUtils";
import {
    FormContainer,
    FormHeader,
    FormCard,
    FormGroup,
    FormSelect,
    FormTextarea,
    ProcessingIndicator,
    ProgressBar,
    FormButton,
    UploadZone,
    StatusBadge,
    ValidationMessage
} from './FormComponents';

const GoodPracticesForm = ({ selectedAuditGMSOne, gms, aformData, handleChange, handleFileChange, selectedAuditGmsThree, selectedCategory, handleCategoryChange }) => {
    // Form fields for Good Practices
    const [file, setFile] = useState([])
    const [gmsThreeData, setGmsThreeData] = useState(null)
    const [gmsTwoTitle, setGmsTwoTitle] = useState('')
    const [isProcessingFiles, setIsProcessingFiles] = useState(false)

    const fetchImageFile = async (url) => {
        const response = await fetch(url);
        const blob = await response.blob();
        const file = new File([blob], url.split('/').pop(), { type: blob.type });
        console.log(file)
        return file;
    };

    // Fetch GMS 3 data and then GMS 2 title
    useEffect(() => {
        const fetchGmsTwoTitle = async () => {
            if (selectedAuditGmsThree) {
                try {
                    // Get GMS 3 data
                    const gmsThreeResponse = await API.get(AUDIT_GMS3_WITH_ID_URL(selectedAuditGmsThree));
                    if (gmsThreeResponse.status === 200) {
                        const gmsThreeItem = gmsThreeResponse.data;
                        setGmsThreeData(gmsThreeItem);

                        // Get GMS 2 data using auditGmsTwoId
                        if (gmsThreeItem.auditGmsTwoId) {
                            const gmsTwoResponse = await API.get(AUDIT_GMS2_WITH_ID_URL(gmsThreeItem.auditGmsTwoId));
                            if (gmsTwoResponse.status === 200) {
                                setGmsTwoTitle(gmsTwoResponse.data.name);
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error fetching GMS data:', error);
                }
            }
        };

        fetchGmsTwoTitle();
    }, [selectedAuditGmsThree]);

    useEffect(() => {

        const addFiles = async () => {
            const urls = aformData.uploads ? aformData.uploads.map(i => {
                return `${STATIC_URL}/${i}`
            }) : []
            const fetchedFiles = await Promise.all(urls.map(url => fetchImageFile(url)));
            console.log(fetchedFiles)
            setFile(fetchedFiles)

        }
        addFiles();

    }, [aformData.uploads])

    // Custom file change handler that processes HEIC files
    const handleCustomFileChange = async (files) => {
        if (!files || files.length === 0) {
            handleFileChange(files);
            return;
        }

        try {
            setIsProcessingFiles(true);
            const processedFiles = await processUploadedFiles(files, 0.8);
            handleFileChange(processedFiles);
        } catch (error) {
            console.error('Error processing files:', error);
            alert('Error processing HEIC files. Please try again or use JPG/PNG format.');
        } finally {
            setIsProcessingFiles(false);
        }
    };

    return (
        <FormContainer theme="goodPractices">
            {/* Progress Indicator */}
            <ProgressBar progress={75} />

            {/* Header Section */}
            <FormHeader
                title={gmsTwoTitle || 'Good Practices Form'}
                subtitle="Document positive practices and commendable observations that demonstrate excellence in safety, quality, and operational standards"
                icon="mdi mdi-check-circle-outline"
                theme="goodPractices"
            />

            <form className="audit-form">
                {/* Category Selection Card */}
                <FormCard
                    title="Category Selection"
                    icon="mdi mdi-tag-outline"
                >
                    <FormGroup
                        label="Category"
                        required={true}
                        helpText="Select the type of finding you want to document"
                    >
                        <FormSelect
                            value={selectedCategory}
                            onChange={handleCategoryChange}
                            id="categorySelect"
                            required={true}
                            large={true}
                        >
                            <option value="">Choose a category...</option>
                            <option value="Good Practices">✅ Good Practices</option>
                            <option value="Non-Conformances">⚠️ Non-Conformances</option>
                            <option value="Opportunity For Improvement">💡 Opportunity For Improvement</option>
                        </FormSelect>
                    </FormGroup>

                    {selectedCategory && (
                        <StatusBadge type={selectedCategory === 'Good Practices' ? 'success' : selectedCategory === 'Non-Conformances' ? 'danger' : 'warning'}>
                            {selectedCategory} Selected
                        </StatusBadge>
                    )}
                </FormCard>

                {/* Evidence Upload Card */}
                <FormCard
                    title="Evidence Documentation"
                    icon="mdi mdi-camera-outline"
                >
                    <FormGroup
                        label="Upload Photos"
                        helpText="Upload supporting evidence images (Max 5 files, 100MB each - Supports JPG, PNG, HEIC)"
                    >
                        {isProcessingFiles && (
                            <ProcessingIndicator message="Converting HEIC files and optimizing images..." />
                        )}

                        <UploadZone
                            isDragActive={false}
                            className="enhanced-upload-zone"
                        >
                            {file.length !== 0 ? (
                                <DropzoneArea
                                    initialFiles={file}
                                    acceptedFiles={getAcceptedFileTypes().split(',')}
                                    dropzoneText="📸 Drag and drop evidence images here or click to browse"
                                    filesLimit={5}
                                    maxFileSize={104857600}
                                    onChange={handleCustomFileChange}
                                    previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                                    showPreviews={true}
                                    showPreviewsInDropzone={false}
                                    disabled={isProcessingFiles}
                                />
                            ) : (
                                <DropzoneArea
                                    acceptedFiles={getAcceptedFileTypes().split(',')}
                                    dropzoneText="📸 Drag and drop evidence images here or click to browse"
                                    filesLimit={5}
                                    maxFileSize={104857600}
                                    onChange={handleCustomFileChange}
                                    previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                                    showPreviews={true}
                                    showPreviewsInDropzone={false}
                                    disabled={isProcessingFiles}
                                />
                            )}
                        </UploadZone>
                    </FormGroup>
                </FormCard>

                {/* Findings Card */}
                <FormCard
                    title="Findings & Observations"
                    icon="mdi mdi-text-box-outline"
                >
                    <FormGroup
                        label="Detailed Findings"
                        required={true}
                        helpText="Provide clear, specific details about the positive practice observed, including what was done well and why it's commendable"
                    >
                        <FormTextarea
                            id="findings"
                            name="findings"
                            onChange={handleChange}
                            value={aformData.findings}
                            rows={6}
                            placeholder="Describe the good practice observed in detail. Include specific actions, behaviors, or processes that demonstrate excellence and should be recognized or replicated..."
                            required={true}
                            large={true}
                        />
                    </FormGroup>

                    {aformData.findings && aformData.findings.length > 50 && (
                        <ValidationMessage
                            type="success"
                            message="Great! You've provided detailed findings."
                            show={true}
                        />
                    )}
                </FormCard>
            </form>
        </FormContainer>
    );
};

export default GoodPracticesForm;