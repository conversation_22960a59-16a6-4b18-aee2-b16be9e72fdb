import React, { useState, useEffect, useRef } from 'react';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import 'primereact/resources/themes/saga-blue/theme.css';
import HighchartsWrapper from './HighchartsWrapper';
import moment from 'moment';
import html2canvas from 'html2canvas';
import { Button } from '@material-ui/core';
import GetAppIcon from '@material-ui/icons/GetApp'; // Material UI Download Icon

const MonthlyYearlyDataChart = ({
    initialDate,
    data,
    preprocessData,
    calculateTRIR,
    chartId,
    manHoursKey,
    chartConfig
}) => {
    const [selectedDate, setSelectedDate] = useState(initialDate);
    const [filteredData, setFilteredData] = useState([]);
    const headerRef = useRef(null);

    function calculatePartialRollingTRIR(data, manHoursKey) {
        const months = {
            Jan: 0, Feb: 1, Mar: 2, Apr: 3, May: 4, Jun: 5,
            Jul: 6, Aug: 7, Sep: 8, Oct: 9, Nov: 10, Dec: 11
        };

        // Sort data chronologically
        data.sort((a, b) => {
            const dateA = new Date(a.year, months[a.month]);
            const dateB = new Date(b.year, months[b.month]);
            return dateA - dateB;
        });

        const result = [];

        for (let i = 0; i < data.length; i++) {
            // Look back at most 12 months (i.e. partial if i < 11)
            const rollingData = data.slice(Math.max(0, i - 11), i + 1);

            const totalIncidents = rollingData.reduce((sum, item) => sum + item.noOfRecordableIncidentCases, 0);
            const totalHours = rollingData.reduce((sum, item) => sum + item.monthlyHoursWorked, 0);

            const trir = totalHours > 0 ? (totalIncidents * manHoursKey) / totalHours : 0;

            result.push({
                year: data[i].year,
                month: data[i].month,
                noOfRecordableIncidentCases: data[i].noOfRecordableIncidentCases,
                rollingTRIR: parseFloat(trir.toFixed(3)),
                isPartial: i < 11
            });
        }

        return result;
    }



    useEffect(() => {
        if (selectedDate) {
            const selectedDateMoment = moment(selectedDate);

            // 🔹 STEP 1: PREPROCESS & CALCULATE TRIR FOR ALL DATA (Jan 2023 - Latest Available Month)
            const preProcessedData = preprocessData(data);




            const monthsSet = new Set(preProcessedData.map(item => moment(`${item.year}-${item.month}`, "YYYY-MMM").format("YYYY-MMM")));
            const allMonths = [];
            for (let i = 0; i < 12; i++) {
                const monthMoment = selectedDateMoment.clone().subtract(i, 'months');
                allMonths.push(monthMoment.format("YYYY-MMM"));
            }

            allMonths.forEach(monthYear => {
                if (!monthsSet.has(monthYear)) {
                    const [year, month] = monthYear.split('-');
                    preProcessedData.push({
                        year: parseInt(year),
                        month: month,
                        country: "N/A",
                        bu: "N/A",
                        level: "N/A",
                        averageNoOfSTTGdcEmployeesPerDay: 0,
                        averageNoOfContractorEmployeesPerDay: 0,
                        totalNoOfEmployees: 0,
                        monthlyHoursWorked: 0,
                        cumulativeWorkHours: 0,
                        noOfSafetyInductionsConducted: 0,
                        noOfToolboxMeetingsSafetyBriefingsSafeStarts: 0,
                        noOfEHSTrainings: 0,
                        noOfEHSInspectionsAudits: 0,
                        noOfManagementSiteWalkInspection: 0,
                        authorityNGOUnionVisits: 0,
                        noOfSafeObservations: 0,
                        noOfAtRiskObservations: 0,
                        totalNoOfObservations: 0,
                        noOfFatality: 0,
                        noOfDaysAwayFromWorkCasesLTICases: 0,
                        noOfRestrictedWorkCasesLightDutyJobTransfer: 0,
                        noOfLossOfConsciousnessCases: 0,
                        noOfMedicalTreatmentCases: 0,
                        noOfHealthRelatedCases: 0,
                        noOfRecordableIncidentCases: 0,
                        legalAndOtherNonCompliances: 0,
                        noticesOrStopWorkOrders: 0,
                        authorityReportableIncident: 0,
                        noOfNearMissCases: 0,
                        noOfSeriousPotentiallySeriousIncidentsDangerousOccurrence: 0,
                        noOfFirstAidCases: 0,
                        authorityNgoUnionVisits: null

                    });
                }
            });

            const processedData = calculatePartialRollingTRIR(preProcessedData, manHoursKey);



            let filtered = processedData.filter(item => {
                const itemDateMoment = moment(`${item.year}-${item.month}`, "YYYY-MMM");
                return itemDateMoment.isSameOrBefore(selectedDateMoment, 'month') &&
                    itemDateMoment.isSameOrAfter(selectedDateMoment.clone().subtract(11, 'months'), 'month');
            });






            filtered.sort((a, b) => {
                // Sort by Year first
                if (a.year !== b.year) return a.year - b.year;

                // Convert "Jan", "Feb", etc. into numeric values (01, 02, ..., 12)
                const monthA = moment(a.month, "MMM").format("MM");
                const monthB = moment(b.month, "MMM").format("MM");

                return parseInt(monthA) - parseInt(monthB);
            });
            setFilteredData(filtered);
        }
    }, [selectedDate, data]);

    const finalChartData = filteredData.length > 0 ? filteredData : [];
    const categories = finalChartData.map((item) => {
        const shortYear = item.year.toString().slice(-2); // Extract last two digits
        return `${item.month} '${shortYear}`;
    });
    const barData = finalChartData.map((item) => item[chartConfig.barKey] || 0);
    const lineData = finalChartData.map((item) => item[chartConfig.lineKey] || 0);

    const options = {
        chart: { zoomType: 'xy' },
        title: { text: `` },
        xAxis: {
            categories, crosshair: true, labels: {
                style: {
                    fontSize: '15px', // Adjust this value as needed

                }
            }
        },
        yAxis: [
            {
                title: {
                    text: chartConfig.barLegend, style: {
                        fontSize: '15px', // Adjust font size for Y-Axis title

                    }
                },
                opposite: false,
                allowDecimals: false,
                tickInterval: 1,
                min: 0,
                labels: {
                    style: {
                        fontSize: '15px', // Adjust this value as needed

                    }
                }
            },
            {
                title: {
                    text: chartConfig.lineLegend, style: {
                        fontSize: '15px', // Adjust font size for Y-Axis title

                    }
                }, opposite: true, labels: {
                    style: {
                        fontSize: '15px', // Adjust this value as needed

                    }
                }
            },
        ],
        tooltip: { shared: true },
        legend: {
            itemStyle: {
                fontSize: '15px',  // Adjust the font size for series name

            }
        },
        series: [
            {
                name: chartConfig.barLegend,
                type: 'column',
                data: barData,
                color: chartConfig.barColor || '#007bff',
                yAxis: 0,
                dataLabels: {
                    enabled: true,
                    inside: true,
                    format: '{y}',
                    verticalAlign: 'bottom', // Align at the bottom of the bar
                    align: 'center',
                    y: 0,
                    style: {
                        textOutline: 'white',
                        color: 'black',
                        fontSize: '15px', // Adjust the font size for data labels

                    }
                },
            },
            {
                name: chartConfig.lineLegend,
                type: 'spline',
                data: lineData,
                color: chartConfig.lineColor || '#FF5733',
                yAxis: 1,
                dataLabels: {
                    enabled: true, format: '{y}', style: {
                        fontSize: '15px', // Adjust the font size for data labels
                        fontWeight: 'normal'
                    }
                },
            },
        ],
    };

    // Function to download the chart header
    const downloadHeader = async () => {
        if (headerRef.current) {
            const canvas = await html2canvas(headerRef.current);
            const link = document.createElement('a');
            link.href = canvas.toDataURL('image/png');
            link.download = `chart-header-${chartId}.png`;
            link.click();
        }
    };

    console.log(chartId, manHoursKey, 'chartId, manHoursKey');

    return (
        <div className='card shadow'>
            <div className='card-body' ref={headerRef}>
                <div className='chart-header d-flex justify-content-between' >
                    <div className="left-content">
                        <h5 className='font-weight-bold'>
                            {chartConfig.title} for every {' '}
                            <select
                                id={`manHoursChart${chartId}`}
                                value={manHoursKey}
                                onChange={(e) => chartConfig.onManHoursChange(`chart${chartId}`, Number(e.target.value))}
                            >
                                <option value={200000}>200,000</option>
                                <option value={1000000}>1,000,000</option>
                            </select>{' '}
                            hours worked as of{' '}
                            <Calendar
                                value={selectedDate}
                                onChange={(e) => setSelectedDate(e.value)}
                                view="month"
                                yearNavigator
                                yearRange="2010:2030"
                                monthNavigator
                                showIcon
                                dateFormat="M yy"
                                placeholder="Select Month"
                                minDate={moment().set({ year: 2023, month: 0, date: 1 }).toDate()}
                                maxDate={moment().endOf('month').toDate()}
                            />
                        </h5>
                    </div>
                    <div>
                        <GetAppIcon
                            onClick={downloadHeader}
                            style={{
                                fontSize: '18px',
                                cursor: 'pointer',
                                color: 'black',
                            }}
                        />
                    </div>
                </div>
                <HighchartsWrapper options={options} />
            </div>
        </div>
    );
};

export default MonthlyYearlyDataChart;
