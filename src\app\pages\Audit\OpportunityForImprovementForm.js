import React, { useState, useEffect } from "react";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../../services/API";
import { AUDIT_FINDINGS_ASSIGN_ACTION_URL, AUDIT_GMS3_URL, AUDIT_WITH_ID_URL, GET_USERS_BY_ROLE, INSPECTION_ACTION_PLAN_REVIEWER, STATIC_URL, AUDIT_GMS3_WITH_ID_URL, AUDIT_GMS2_WITH_ID_URL } from "../../constants";
import { processUploadedFiles, getAcceptedFileTypes } from "./utils/imageUtils";
const OpportunityForImprovementForm = ({ selectedAuditGMSOne, gms, aformData, handleChange, handleFileChange, selectedAuditGmsThree, selectedCategory, handleCategoryChange }) => {
    // Form fields for Area For Improvement
    const [file, setFile] = useState([])
    const [gmsThreeData, setGmsThreeData] = useState(null)
    const [gmsTwoTitle, setGmsTwoTitle] = useState('')
    const [isProcessingFiles, setIsProcessingFiles] = useState(false)

    const fetchImageFile = async (url) => {
        const response = await fetch(url);
        const blob = await response.blob();
        const file = new File([blob], url.split('/').pop(), { type: blob.type });
        console.log(file)
        return file;
    };

    // Fetch GMS 3 data and then GMS 2 title
    useEffect(() => {
        const fetchGmsTwoTitle = async () => {
            if (selectedAuditGmsThree) {
                try {
                    // Get GMS 3 data
                    const gmsThreeResponse = await API.get(AUDIT_GMS3_WITH_ID_URL(selectedAuditGmsThree));
                    if (gmsThreeResponse.status === 200) {
                        const gmsThreeItem = gmsThreeResponse.data;
                        setGmsThreeData(gmsThreeItem);

                        // Get GMS 2 data using auditGmsTwoId
                        if (gmsThreeItem.auditGmsTwoId) {
                            const gmsTwoResponse = await API.get(AUDIT_GMS2_WITH_ID_URL(gmsThreeItem.auditGmsTwoId));
                            if (gmsTwoResponse.status === 200) {
                                setGmsTwoTitle(gmsTwoResponse.data.name);
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error fetching GMS data:', error);
                }
            }
        };

        fetchGmsTwoTitle();
    }, [selectedAuditGmsThree]);

    useEffect(() => {
        console.log(selectedAuditGMSOne, ' gms one')
        const addFiles = async () => {
            const urls = aformData.uploads ? aformData.uploads.map(i => {
                return `${STATIC_URL}/${i}`
            }) : []
            const fetchedFiles = await Promise.all(urls.map(url => fetchImageFile(url)));
            console.log(fetchedFiles)
            setFile(fetchedFiles)

        }
        addFiles();

    }, [aformData.uploads])

    // Custom file change handler that processes HEIC files
    const handleCustomFileChange = async (files) => {
        if (!files || files.length === 0) {
            handleFileChange(files);
            return;
        }

        try {
            setIsProcessingFiles(true);
            const processedFiles = await processUploadedFiles(files, 0.8);
            handleFileChange(processedFiles);
        } catch (error) {
            console.error('Error processing files:', error);
            alert('Error processing HEIC files. Please try again or use JPG/PNG format.');
        } finally {
            setIsProcessingFiles(false);
        }
    };

    return (
        <div className="audit-form-container">
            {/* Header Section */}
            <div className="form-header mb-4">
                <h2 className="form-title text-warning mb-2">
                    <i className="mdi mdi-lightbulb-outline me-2"></i>
                    {gmsTwoTitle || 'Opportunity For Improvement Form'}
                </h2>
                <p className="form-subtitle text-muted mb-0">
                    Identify areas for enhancement and continuous improvement opportunities
                </p>
            </div>

            <form className="audit-form">
                {/* Category Selection Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-tag-outline me-2"></i>
                            Category Selection
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group">
                            <label htmlFor="categorySelect" className="form-label fw-semibold">
                                Category <span className="text-danger">*</span>
                            </label>
                            <select
                                className="form-select form-control-lg"
                                value={selectedCategory}
                                onChange={handleCategoryChange}
                                id="categorySelect"
                                required
                            >
                                <option value="">Select Category...</option>
                                <option value="Good Practices">✓ Good Practices</option>
                                <option value="Non-Conformances">⚠ Non-Conformances</option>
                                <option value="Opportunity For Improvement">💡 Opportunity For Improvement</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* Findings Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-text-box-outline me-2"></i>
                            Current State Analysis
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group">
                            <label htmlFor="findings" className="form-label fw-semibold">
                                Current Situation & Findings <span className="text-danger">*</span>
                            </label>
                            <textarea
                                className="form-control form-control-lg"
                                id="findings"
                                name="findings"
                                value={aformData.findings}
                                onChange={handleChange}
                                rows="4"
                                placeholder="Describe the current situation, what you observed, and why there's an opportunity for improvement..."
                                required
                            ></textarea>
                            <div className="form-text">
                                <small className="text-muted">
                                    <i className="mdi mdi-information-outline me-1"></i>
                                    Provide context about the current state and why improvement is needed
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Evidence Upload Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-camera-outline me-2"></i>
                            Supporting Evidence
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group">
                            <label htmlFor="evidencePhotos" className="form-label fw-semibold">
                                Upload Photos (Evidence)
                                <small className="text-muted ms-2">(Max 5 files, 100MB each - Supports JPG, PNG, HEIC)</small>
                            </label>
                            <div className="upload-zone">
                                {isProcessingFiles && (
                                    <div className="processing-indicator">
                                        <i className="mdi mdi-loading mdi-spin me-2"></i>
                                        Converting HEIC files...
                                    </div>
                                )}
                                {file.length !== 0 ? (
                                    <DropzoneArea
                                        initialFiles={file}
                                        acceptedFiles={getAcceptedFileTypes().split(',')}
                                        dropzoneText="Drag and drop evidence images here or click to browse (JPG, PNG, HEIC supported)"
                                        filesLimit={5}
                                        maxFileSize={104857600}
                                        onChange={handleCustomFileChange}
                                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                                        showPreviews={true}
                                        showPreviewsInDropzone={false}
                                        disabled={isProcessingFiles}
                                    />
                                ) : (
                                    <DropzoneArea
                                        acceptedFiles={getAcceptedFileTypes().split(',')}
                                        dropzoneText="Drag and drop evidence images here or click to browse (JPG, PNG, HEIC supported)"
                                        filesLimit={5}
                                        maxFileSize={104857600}
                                        onChange={handleCustomFileChange}
                                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                                        showPreviews={true}
                                        showPreviewsInDropzone={false}
                                        disabled={isProcessingFiles}
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Standards & References Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-book-outline me-2"></i>
                            Standards & Best Practices
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group">
                            <label htmlFor="standardsAndReferences" className="form-label fw-semibold">
                                Relevant Standards & References
                            </label>
                            <input
                                type="text"
                                className="form-control form-control-lg"
                                id="standardsAndReferences"
                                name="standardsAndReferences"
                                value={aformData.standardsAndReferences}
                                onChange={handleChange}
                                placeholder="e.g., ISO 14001, Best Practice Guidelines, Industry Standards..."
                            />
                            <div className="form-text">
                                <small className="text-muted">
                                    <i className="mdi mdi-information-outline me-1"></i>
                                    Reference applicable standards or best practices that could guide the improvement
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Improvement Recommendations Card */}
                <div className="form-card mb-4">
                    <div className="card-header">
                        <h5 className="card-title mb-0">
                            <i className="mdi mdi-trending-up me-2"></i>
                            Improvement Opportunities
                        </h5>
                    </div>
                    <div className="card-body">
                        <div className="form-group">
                            <label htmlFor="recommendations" className="form-label fw-semibold">
                                Recommended Improvements <span className="text-danger">*</span>
                            </label>
                            <textarea
                                className="form-control form-control-lg"
                                id="recommendations"
                                name="recommendations"
                                value={aformData.recommendations}
                                onChange={handleChange}
                                rows="4"
                                placeholder="Describe specific improvement opportunities, potential benefits, and suggested implementation approaches..."
                                required
                            ></textarea>
                            <div className="form-text">
                                <small className="text-muted">
                                    <i className="mdi mdi-information-outline me-1"></i>
                                    Provide actionable recommendations that could enhance performance or efficiency
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <style jsx>{`
                .audit-form-container {
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }

                .form-header {
                    text-align: center;
                    border-bottom: 2px solid #e9ecef;
                    padding-bottom: 20px;
                }

                .form-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #ffc107;
                }

                .form-subtitle {
                    font-size: 1rem;
                    color: #6c757d;
                }

                .form-card {
                    background: #fff;
                    border: 1px solid #e9ecef;
                    border-radius: 12px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                    transition: box-shadow 0.3s ease;
                }

                .form-card:hover {
                    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                }

                .card-header {
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border-bottom: 1px solid #e9ecef;
                    padding: 15px 20px;
                    border-radius: 12px 12px 0 0;
                }

                .card-title {
                    color: #495057;
                    font-weight: 600;
                }

                .card-body {
                    padding: 20px;
                }

                .form-label {
                    color: #495057;
                    margin-bottom: 8px;
                }

                .form-control, .form-select {
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    padding: 12px 16px;
                    transition: all 0.3s ease;
                }

                .form-control:focus, .form-select:focus {
                    border-color: #ffc107;
                    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
                }

                .form-control-lg {
                    font-size: 1rem;
                    padding: 14px 18px;
                }

                .upload-zone {
                    border: 2px dashed #e9ecef;
                    border-radius: 8px;
                    transition: border-color 0.3s ease;
                }

                .upload-zone:hover {
                    border-color: #ffc107;
                }

                .processing-indicator {
                    background-color: #e3f2fd;
                    border: 1px solid #2196f3;
                    border-radius: 8px;
                    padding: 12px 16px;
                    margin-bottom: 16px;
                    color: #1976d2;
                    font-weight: 500;
                    text-align: center;
                }

                .mdi-spin {
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                .form-text {
                    margin-top: 8px;
                }

                .text-danger {
                    color: #dc3545 !important;
                }

                .text-muted {
                    color: #6c757d !important;
                }

                .text-warning {
                    color: #ffc107 !important;
                }

                .fw-semibold {
                    font-weight: 600;
                }

                .me-1 { margin-right: 0.25rem; }
                .me-2 { margin-right: 0.5rem; }
                .ms-2 { margin-left: 0.5rem; }
                .mb-0 { margin-bottom: 0; }
                .mb-2 { margin-bottom: 0.5rem; }
                .mb-4 { margin-bottom: 1.5rem; }
            `}</style>
        </div>
    );
};

export default OpportunityForImprovementForm;